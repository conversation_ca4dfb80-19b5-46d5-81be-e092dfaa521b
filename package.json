{"name": "quadrate-react", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:client", "build:client": "tsc -b && vite build --outDir dist", "build:server": "echo \"Skipping server build for now\"", "lint": "eslint .", "preview": "vite preview", "start": "NODE_ENV=production node server.js", "deploy": "npm run build && cd dist && git init && git add . && git commit -m 'Deploy' && git push --force https://github.com/nirzaf/qts-react.git main:gh-pages", "generate-sitemap": "node scripts/generate-sitemap.js", "generate-image-sitemap": "node scripts/generate-image-sitemap.js", "generate-video-sitemap": "node scripts/generate-video-sitemap.js", "generate-news-sitemap": "node scripts/generate-news-sitemap.js", "generate-robots": "node scripts/generate-robots.js", "generate-rss": "node scripts/generate-rss.js", "generate-seo": "npm run generate-sitemap && npm run generate-image-sitemap && npm run generate-video-sitemap && npm run generate-news-sitemap && npm run generate-robots && npm run generate-rss", "prebuild": "npm run generate-seo"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@supabase/supabase-js": "^2.49.4", "@types/node": "^22.15.2", "@types/react-helmet": "^6.1.11", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.176.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "date-fns": "^4.1.0", "express": "^5.1.0", "framer-motion": "^12.9.1", "highlight.js": "^11.11.1", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "postcss": "^8.5.3", "prismjs": "^1.30.0", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.5.2", "react-syntax-highlighter": "^15.6.1", "sitemap": "^8.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@eslint/js": "^9.25.1", "@swc/plugin-emotion": "^9.0.3", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "npm-check-updates": "^18.0.1", "rollup-plugin-visualizer": "^5.14.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.4", "vite-plugin-compression": "^0.5.1"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}